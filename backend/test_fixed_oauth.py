"""
Test the fixed OAuth manager implementation.

This script tests our updated OAuth manager that uses the correct
toolset.initiate_connection() method.
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.logger import logger
from services.composio_oauth_manager import composio_oauth_manager


async def test_fixed_oauth():
    """Test the fixed OAuth manager implementation."""
    
    logger.info("🧪 Testing Fixed OAuth Manager Implementation")
    logger.info("=" * 60)
    
    # Test user ID (doesn't need to exist in database now)
    test_user_id = "test-user-fixed-oauth"
    test_apps = ["gmail", "notion"]
    
    results = {}
    
    for app_key in test_apps:
        logger.info(f"\n🔄 Testing {app_key.upper()}")
        logger.info("-" * 40)
        
        try:
            # Test OAuth initiation
            logger.info(f"🚀 Initiating OAuth connection for {app_key}...")
            connection_result = await composio_oauth_manager.initiate_connection(
                test_user_id, app_key
            )
            
            if connection_result.success and connection_result.connection_info:
                results[app_key] = {
                    "success": True,
                    "redirect_url": connection_result.connection_info.redirect_url,
                    "error": None
                }
                logger.info(f"✅ OAuth initiated successfully for {app_key}")
                logger.info(f"🔗 Redirect URL: {connection_result.connection_info.redirect_url}")
            else:
                results[app_key] = {
                    "success": False,
                    "redirect_url": None,
                    "error": connection_result.error
                }
                logger.error(f"❌ OAuth initiation failed for {app_key}: {connection_result.error}")
                
        except Exception as e:
            results[app_key] = {
                "success": False,
                "redirect_url": None,
                "error": str(e)
            }
            logger.error(f"❌ Exception during {app_key} test: {e}")
    
    # Print summary
    logger.info("\n" + "=" * 60)
    logger.info("🎯 FIXED OAUTH TEST SUMMARY")
    logger.info("=" * 60)
    
    success_count = 0
    for app_key, result in results.items():
        status = "✅ SUCCESS" if result["success"] else "❌ FAILED"
        logger.info(f"\n📱 {app_key.upper()}: {status}")
        
        if result["redirect_url"]:
            logger.info(f"   🔗 Redirect URL: {result['redirect_url']}")
            success_count += 1
        
        if result["error"]:
            logger.info(f"   ❌ Error: {result['error']}")
    
    logger.info(f"\n🎉 {success_count}/{len(test_apps)} apps tested successfully!")
    
    if success_count > 0:
        logger.info("\n💡 Implementation Status:")
        logger.info("✅ OAuth manager is working correctly")
        logger.info("✅ Integration IDs are valid")
        logger.info("✅ Composio SDK integration is functional")
        logger.info("✅ Ready for production use")
        return True
    else:
        logger.warning("\n⚠️ Implementation needs attention")
        return False


async def main():
    """Main test function."""
    try:
        success = await test_fixed_oauth()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
