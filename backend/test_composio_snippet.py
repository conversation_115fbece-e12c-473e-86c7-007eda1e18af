"""
Test Composio using the exact code snippet provided.

This script tests the Composio SDK using the exact pattern from the documentation.
"""

import sys
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.logger import logger
from constants.composio_mcp_constants import get_composio_integration_id

# Import Composio SDK
try:
    from composio import ComposioToolSet, App
    COMPOSIO_AVAILABLE = True
    logger.info("✅ Composio SDK imported successfully")
except ImportError:
    logger.error("❌ composio-core not installed. Install with: pip install composio-core")
    ComposioToolSet = None
    App = None
    COMPOSIO_AVAILABLE = False


def test_composio_snippet():
    """Test using the exact Composio code snippet pattern."""
    
    if not COMPOSIO_AVAILABLE:
        logger.error("❌ Composio SDK not available")
        return False
    
    try:
        # Initialize toolset
        toolset = ComposioToolSet()
        logger.info("✅ Composio toolset initialized")
        
        # Test user ID
        user_id = "test_user_123"
        
        # Test with our integration IDs
        test_apps = {
            "gmail": get_composio_integration_id("gmail"),
            "notion": get_composio_integration_id("notion")
        }
        
        for app_name, integration_id in test_apps.items():
            if not integration_id:
                logger.error(f"❌ No integration ID found for {app_name}")
                continue
                
            logger.info(f"\n🔄 Testing {app_name} with integration_id: {integration_id}")
            
            try:
                # Get entity
                entity = toolset.get_entity(id=user_id)
                logger.info(f"✅ Entity created/retrieved: {user_id}")
                
                # Method 1: Try the snippet approach (toolset.initiate_connection)
                logger.info("🚀 Method 1: Using toolset.initiate_connection...")
                try:
                    connection_request = toolset.initiate_connection(
                        integration_id=integration_id,
                        entity_id=user_id,
                        # Optional parameters from snippet
                        # redirect_url="https://yourapp.com/final-destination",
                        # app=App.APP_NAME  # if needed
                    )
                    
                    # Check if redirect URL was provided
                    if hasattr(connection_request, 'redirectUrl') and connection_request.redirectUrl:
                        logger.info(f"✅ Method 1 SUCCESS - Redirect URL: {connection_request.redirectUrl}")
                        return True
                    else:
                        logger.warning("⚠️ Method 1: No redirect URL received")
                        
                except Exception as e1:
                    logger.warning(f"⚠️ Method 1 failed: {e1}")
                
                # Method 2: Try the entity approach (entity.initiate_connection)
                logger.info("🚀 Method 2: Using entity.initiate_connection...")
                try:
                    connection_request = entity.initiate_connection(integration_id)
                    
                    # Check if redirect URL was provided
                    if hasattr(connection_request, 'redirectUrl') and connection_request.redirectUrl:
                        logger.info(f"✅ Method 2 SUCCESS - Redirect URL: {connection_request.redirectUrl}")
                        return True
                    else:
                        logger.warning("⚠️ Method 2: No redirect URL received")
                        
                except Exception as e2:
                    logger.warning(f"⚠️ Method 2 failed: {e2}")
                
                # Method 3: Try with App enum if available
                logger.info("🚀 Method 3: Using App enum approach...")
                try:
                    # Try to get the app enum
                    app_enum = getattr(App, app_name.upper(), None)
                    if app_enum:
                        connection_request = toolset.initiate_connection(
                            integration_id=integration_id,
                            entity_id=user_id,
                            app=app_enum
                        )
                        
                        if hasattr(connection_request, 'redirectUrl') and connection_request.redirectUrl:
                            logger.info(f"✅ Method 3 SUCCESS - Redirect URL: {connection_request.redirectUrl}")
                            return True
                        else:
                            logger.warning("⚠️ Method 3: No redirect URL received")
                    else:
                        logger.warning(f"⚠️ Method 3: No App enum found for {app_name}")
                        
                except Exception as e3:
                    logger.warning(f"⚠️ Method 3 failed: {e3}")
                
            except Exception as e:
                logger.error(f"❌ Failed to test {app_name}: {e}")
                continue
        
        logger.error("❌ All methods failed for all apps")
        return False
        
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")
        return False


def main():
    """Main test function."""
    logger.info("🧪 Testing Composio using documentation snippet")
    logger.info("=" * 60)
    
    success = test_composio_snippet()
    
    if success:
        logger.info("\n🎉 Test successful! At least one method worked.")
        return 0
    else:
        logger.warning("\n⚠️ All test methods failed.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
