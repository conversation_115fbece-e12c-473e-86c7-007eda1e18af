"""
Composio MCP Constants and Utilities

This module provides constants and utilities for working with Composio MCP servers.
It loads the server URLs from the JSON constants file and provides helper functions
for URL generation and validation.
"""

import json
import os
from typing import Dict, Optional, List
from utils.logger import logger

# Path to the constants file
CONSTANTS_FILE_PATH = os.path.join(
    os.path.dirname(__file__), "composio_mcp_servers.json"
)


class ComposioMCPConstants:
    """Utility class for managing Composio MCP server constants."""

    _app_configs: Optional[Dict[str, Dict[str, str]]] = None

    @classmethod
    def _load_constants(cls) -> Dict[str, Dict[str, str]]:
        """Load MCP app configurations from the constants file."""
        if cls._app_configs is not None:
            return cls._app_configs

        try:
            with open(CONSTANTS_FILE_PATH, "r") as f:
                cls._app_configs = json.load(f)
            logger.info(
                f"Loaded {len(cls._app_configs)} Composio MCP app configurations from constants file"
            )
            return cls._app_configs
        except FileNotFoundError:
            logger.error(
                f"Composio MCP constants file not found at {CONSTANTS_FILE_PATH}"
            )
            cls._app_configs = {}
            return cls._app_configs
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing Composio MCP constants file: {e}")
            cls._app_configs = {}
            return cls._app_configs
        except Exception as e:
            logger.error(f"Unexpected error loading Composio MCP constants: {e}")
            cls._app_configs = {}
            return cls._app_configs

    @classmethod
    def get_integration_id(cls, app_key: str) -> Optional[str]:
        """
        Get the integration ID for a given app key.

        Args:
            app_key: The app key (e.g., "gmail", "slack", "github")

        Returns:
            The integration ID or None if not found
        """
        app_configs = cls._load_constants()
        app_config = app_configs.get(app_key, {})
        return app_config.get("integration_id")

    @classmethod
    def get_server_id(cls, app_key: str) -> Optional[str]:
        """
        Get the server ID for a given app key.

        Args:
            app_key: The app key (e.g., "gmail", "slack", "github")

        Returns:
            The server ID or None if not found
        """
        app_configs = cls._load_constants()
        app_config = app_configs.get(app_key, {})
        return app_config.get("server_id")

    @classmethod
    def get_app_config(cls, app_key: str) -> Dict[str, str]:
        """
        Get the full configuration for a given app key.

        Args:
            app_key: The app key (e.g., "gmail", "slack", "github")

        Returns:
            The app configuration dictionary or empty dict if not found
        """
        app_configs = cls._load_constants()
        return app_configs.get(app_key, {})

    @classmethod
    def generate_mcp_url_with_connected_account(
        cls, app_key: str, connected_account_id: str
    ) -> Optional[str]:
        """
        Generate an MCP URL using connected_account_id format.

        Args:
            app_key: The app key (e.g., "gmail", "slack", "github")
            connected_account_id: The connected account ID from Composio

        Returns:
            The complete MCP URL or None if app_key not found
        """
        server_id = cls.get_server_id(app_key)
        if not server_id:
            logger.warning(f"No server ID found for app key: {app_key}")
            return None

        # Generate URL with connected_account_id format
        mcp_url = f"https://mcp.composio.dev/composio/server/{server_id}/mcp?connected_account_id={connected_account_id}"
        logger.info(f"Generated MCP URL for {app_key}: {mcp_url}")
        return mcp_url

    @classmethod
    def get_supported_apps(cls) -> List[str]:
        """
        Get list of all supported app keys.

        Returns:
            List of supported app keys
        """
        app_configs = cls._load_constants()
        return list(app_configs.keys())

    @classmethod
    def is_app_supported(cls, app_key: str) -> bool:
        """
        Check if an app key is supported.

        Args:
            app_key: The app key to check

        Returns:
            True if the app is supported, False otherwise
        """
        return app_key in cls.get_supported_apps()

    @classmethod
    def reload_constants(cls) -> None:
        """Force reload of constants from file."""
        cls._app_configs = None
        cls._load_constants()


# Convenience functions for direct access
def get_composio_integration_id(app_key: str) -> Optional[str]:
    """
    Convenience function to get integration ID for an app.

    Args:
        app_key: The app key (e.g., "gmail", "slack", "github")

    Returns:
        The integration ID or None if app_key not found
    """
    return ComposioMCPConstants.get_integration_id(app_key)


def get_composio_mcp_url_with_connected_account(
    app_key: str, connected_account_id: str
) -> Optional[str]:
    """
    Convenience function to generate MCP URL with connected account ID.

    Args:
        app_key: The app key (e.g., "gmail", "slack", "github")
        connected_account_id: The connected account ID from Composio

    Returns:
        The complete MCP URL or None if app_key not found
    """
    return ComposioMCPConstants.generate_mcp_url_with_connected_account(
        app_key, connected_account_id
    )


def get_composio_app_config(app_key: str) -> Dict[str, str]:
    """
    Convenience function to get full app configuration.

    Args:
        app_key: The app key (e.g., "gmail", "slack", "github")

    Returns:
        The app configuration dictionary or empty dict if not found
    """
    return ComposioMCPConstants.get_app_config(app_key)


def get_supported_composio_apps() -> List[str]:
    """
    Convenience function to get list of supported Composio apps.

    Returns:
        List of supported app keys
    """
    return ComposioMCPConstants.get_supported_apps()


def is_composio_app_supported(app_key: str) -> bool:
    """
    Convenience function to check if an app is supported.

    Args:
        app_key: The app key to check

    Returns:
        True if the app is supported, False otherwise
    """
    return ComposioMCPConstants.is_app_supported(app_key)
