"""
Composio Entity Management Service

This service manages Composio entities for users, implementing the proper
authentication flow as described in composio_auth.md.

Key responsibilities:
1. Create and manage entity IDs for users using Composio SDK
2. Map user_id to entity_id for proper user management
3. Handle entity lifecycle and cleanup
4. Provide entity retrieval and validation
"""

import os
import hashlib
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from utils.logger import logger
from utils.config import config
from supabase import create_client, Client

try:
    from composio import ComposioToolSet

    Entity = None  # Entity objects are obtained from toolset.get_entity()
except ImportError:
    logger.warning(
        "composio-core not installed. Install with: pip install composio-core"
    )
    ComposioToolSet = None
    Entity = None


@dataclass
class EntityInfo:
    """Information about a Composio entity"""

    entity_id: str
    user_id: str
    created_at: str
    is_active: bool
    metadata: Optional[Dict[str, Any]] = None


class ComposioEntityManager:
    """
    Service for managing Composio entities for users.

    This implements the entity management pattern from composio_auth.md:
    - Create entity IDs for users using toolset.get_entity(id=user_identifier)
    - Store entity mappings in database
    - Provide entity retrieval and lifecycle management
    """

    def __init__(self):
        """Initialize the entity manager with Composio SDK and database connection."""
        if not ComposioToolSet:
            logger.warning(
                "composio-core package not available. Some features will be disabled."
            )
            self.toolset = None
        else:
            # Initialize Composio SDK
            self.composio_api_key = config.COMPOSIO_API_KEY
            if not self.composio_api_key:
                logger.warning(
                    "COMPOSIO_API_KEY not set. Composio features will be disabled."
                )
                self.toolset = None
            else:
                # Initialize toolset with API key
                self.toolset = ComposioToolSet(api_key=self.composio_api_key)

        # Initialize Supabase client
        self.supabase: Client = create_client(
            config.SUPABASE_URL, config.SUPABASE_SERVICE_ROLE_KEY
        )

        logger.info("Composio Entity Manager initialized successfully")

    def _generate_entity_id(self, user_id: str) -> str:
        """
        Generate a deterministic entity ID for a user.

        This ensures the same user always gets the same entity ID,
        which is important for consistency across sessions.

        Args:
            user_id: The user's UUID

        Returns:
            A deterministic entity ID string
        """
        # Create a deterministic entity ID based on user_id
        # This ensures consistency across sessions
        entity_data = f"atlas_user_{user_id}"
        entity_hash = hashlib.sha256(entity_data.encode()).hexdigest()
        return f"atlas_{entity_hash[:16]}"

    async def get_or_create_entity(self, user_id: str) -> EntityInfo:
        """
        Get existing entity for user or create a new one.

        This implements the pattern from composio_auth.md:
        entity = toolset.get_entity(id=user_identifier_from_my_app)

        Args:
            user_id: The user's UUID

        Returns:
            EntityInfo object with entity details

        Raises:
            Exception: If entity creation fails
        """
        try:
            logger.info(f"Getting or creating Composio entity for user {user_id}")

            # Generate deterministic entity ID
            entity_id = self._generate_entity_id(user_id)

            # Check if entity already exists in our database
            existing_entity = await self._get_stored_entity(user_id)
            if existing_entity:
                logger.info(
                    f"Found existing entity {existing_entity.entity_id} for user {user_id}"
                )
                return existing_entity

            # Create new entity using Composio SDK
            logger.info(f"Creating new Composio entity {entity_id} for user {user_id}")

            # Get entity from Composio (this creates it if it doesn't exist)
            if not self.toolset:
                logger.error("Composio toolset not available")
                raise Exception("Composio SDK not properly initialized")

            entity = self.toolset.get_entity(id=entity_id)

            # Store entity information in our database
            entity_info = EntityInfo(
                entity_id=entity_id,
                user_id=user_id,
                created_at="now()",
                is_active=True,
                metadata={"source": "atlas_backend", "version": "1.0"},
            )

            await self._store_entity(entity_info)

            logger.info(
                f"Successfully created and stored entity {entity_id} for user {user_id}"
            )
            return entity_info

        except Exception as e:
            logger.error(f"Error getting or creating entity for user {user_id}: {e}")
            raise

    async def get_entity(self, user_id: str) -> Optional[EntityInfo]:
        """
        Get existing entity for user.

        Args:
            user_id: The user's UUID

        Returns:
            EntityInfo object if found, None otherwise
        """
        try:
            return await self._get_stored_entity(user_id)
        except Exception as e:
            logger.error(f"Error getting entity for user {user_id}: {e}")
            return None

    def get_composio_entity(self, entity_id: str) -> Optional[Any]:
        """
        Get Composio Entity object for use with SDK operations.

        Args:
            entity_id: The entity ID

        Returns:
            Composio Entity object or None if not found
        """
        try:
            if not self.toolset:
                logger.error("Composio toolset not available")
                return None
            return self.toolset.get_entity(id=entity_id)
        except Exception as e:
            logger.error(f"Error getting Composio entity {entity_id}: {e}")
            return None

    async def _get_stored_entity(self, user_id: str) -> Optional[EntityInfo]:
        """Get entity information from database."""
        try:
            # Query the composio_entities table
            result = (
                self.supabase.table("composio_entities")
                .select("*")
                .eq("user_id", user_id)
                .eq("is_active", True)
                .execute()
            )

            if result.data:
                entity_data = result.data[0]
                return EntityInfo(
                    entity_id=entity_data["entity_id"],
                    user_id=entity_data["user_id"],
                    created_at=entity_data["created_at"],
                    is_active=entity_data["is_active"],
                    metadata=entity_data.get("metadata"),
                )

            return None

        except Exception as e:
            logger.error(f"Error querying stored entity for user {user_id}: {e}")
            return None

    async def _store_entity(self, entity_info: EntityInfo) -> bool:
        """Store entity information in database."""
        try:
            # Insert into composio_entities table
            result = (
                self.supabase.table("composio_entities")
                .insert(
                    {
                        "entity_id": entity_info.entity_id,
                        "user_id": entity_info.user_id,
                        "is_active": entity_info.is_active,
                        "metadata": entity_info.metadata or {},
                    }
                )
                .execute()
            )

            if result.data:
                logger.info(
                    f"Successfully stored entity {entity_info.entity_id} for user {entity_info.user_id}"
                )
                return True
            else:
                logger.error(f"Failed to store entity {entity_info.entity_id}")
                return False

        except Exception as e:
            logger.error(f"Error storing entity {entity_info.entity_id}: {e}")
            return False

    async def deactivate_entity(self, user_id: str) -> bool:
        """
        Deactivate an entity for a user.

        Args:
            user_id: The user's UUID

        Returns:
            True if successful, False otherwise
        """
        try:
            result = (
                self.supabase.table("composio_entities")
                .update({"is_active": False})
                .eq("user_id", user_id)
                .execute()
            )

            if result.data:
                logger.info(f"Successfully deactivated entity for user {user_id}")
                return True
            else:
                logger.warning(f"No entity found to deactivate for user {user_id}")
                return False

        except Exception as e:
            logger.error(f"Error deactivating entity for user {user_id}: {e}")
            return False

    async def list_user_entities(self, user_id: str) -> List[EntityInfo]:
        """
        List all entities for a user (active and inactive).

        Args:
            user_id: The user's UUID

        Returns:
            List of EntityInfo objects
        """
        try:
            result = (
                self.supabase.table("composio_entities")
                .select("*")
                .eq("user_id", user_id)
                .order("created_at", desc=True)
                .execute()
            )

            entities = []
            for entity_data in result.data:
                entities.append(
                    EntityInfo(
                        entity_id=entity_data["entity_id"],
                        user_id=entity_data["user_id"],
                        created_at=entity_data["created_at"],
                        is_active=entity_data["is_active"],
                        metadata=entity_data.get("metadata"),
                    )
                )

            return entities

        except Exception as e:
            logger.error(f"Error listing entities for user {user_id}: {e}")
            return []


# Create singleton instance
composio_entity_manager = ComposioEntityManager()
