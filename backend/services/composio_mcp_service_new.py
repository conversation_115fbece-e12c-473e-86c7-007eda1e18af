"""
New Composio MCP Service

This service replaces the old janky implementation with proper Composio SDK
authentication and MCP URL generation using connected_account_id format.

Key improvements:
1. Uses proper Composio SDK for authentication
2. Implements entity management for users
3. Uses connected_account_id format for MCP URLs
4. Proper OAuth flow with redirect URLs
5. Maintains compatibility with existing frontend API
"""

from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from utils.logger import logger
from services.composio_entity_manager import composio_entity_manager
from services.composio_oauth_manager import composio_oauth_manager, ConnectionInfo
from constants.composio_mcp_constants import (
    get_composio_integration_id,
    get_composio_mcp_url_with_connected_account,
    get_composio_app_config,
    get_supported_composio_apps
)


@dataclass
class ComposioMCPConnection:
    """Result of Composio MCP connection creation - maintains compatibility with old interface"""
    success: bool
    app_key: str
    mcp_url: Optional[str] = None
    session_uuid: Optional[str] = None  # Deprecated but kept for compatibility
    auth_url: Optional[str] = None
    error: Optional[str] = None
    qualified_name: Optional[str] = None


class ComposioMCPServiceNew:
    """
    New Composio MCP Service using proper SDK authentication.
    
    This service maintains the same interface as the old service but uses
    the new Composio SDK-based authentication flow internally.
    """

    def __init__(self):
        """Initialize the new MCP service."""
        logger.info("Initializing new Composio MCP Service with SDK authentication")

    async def create_user_mcp_connection_no_storage(
        self, user_id: str, app_key: str
    ) -> ComposioMCPConnection:
        """
        Create MCP connection without storing in agents table.
        
        This method maintains compatibility with the existing API while using
        the new OAuth flow internally.
        
        Args:
            user_id: User identifier
            app_key: App key (e.g., "gmail", "slack")
            
        Returns:
            ComposioMCPConnection with auth_url for OAuth flow
        """
        try:
            logger.info(f"Creating MCP connection (new flow) for user {user_id}, app {app_key}")
            
            # Validate app is supported
            if not get_composio_integration_id(app_key):
                return ComposioMCPConnection(
                    success=False,
                    app_key=app_key,
                    error=f"App {app_key} is not supported"
                )
            
            # Check if active connection already exists
            existing_connection = await composio_oauth_manager.get_connection(user_id, app_key)
            if existing_connection and existing_connection.status == "active":
                logger.info(f"Active connection already exists for {app_key}")
                return ComposioMCPConnection(
                    success=True,
                    app_key=app_key,
                    mcp_url=existing_connection.mcp_url,
                    auth_url=existing_connection.mcp_url,  # For active connections, MCP URL is ready
                    qualified_name=f"composio/{app_key}"
                )
            
            # Initiate new OAuth connection
            connection_result = await composio_oauth_manager.initiate_connection(user_id, app_key)
            
            if connection_result.success and connection_result.connection_info:
                connection_info = connection_result.connection_info
                return ComposioMCPConnection(
                    success=True,
                    app_key=app_key,
                    mcp_url=None,  # Will be available after OAuth completion
                    auth_url=connection_info.redirect_url,  # This is the OAuth redirect URL
                    qualified_name=f"composio/{app_key}"
                )
            else:
                return ComposioMCPConnection(
                    success=False,
                    app_key=app_key,
                    error=connection_result.error or "Failed to initiate OAuth connection"
                )
                
        except Exception as e:
            logger.error(f"Error in create_user_mcp_connection_no_storage: {e}")
            return ComposioMCPConnection(
                success=False,
                app_key=app_key,
                error=str(e)
            )

    async def wait_for_oauth_completion(self, user_id: str, app_key: str, timeout: int = 180) -> ComposioMCPConnection:
        """
        Wait for OAuth completion and return MCP connection with URL.
        
        This method waits for the user to complete OAuth authentication
        and returns the final MCP URL with connected_account_id.
        
        Args:
            user_id: User identifier
            app_key: App key
            timeout: Timeout in seconds
            
        Returns:
            ComposioMCPConnection with mcp_url if successful
        """
        try:
            logger.info(f"Waiting for OAuth completion for user {user_id}, app {app_key}")
            
            # Wait for connection activation
            connection_result = await composio_oauth_manager.wait_for_connection_activation(
                user_id, app_key, timeout
            )
            
            if connection_result.success and connection_result.connection_info:
                connection_info = connection_result.connection_info
                return ComposioMCPConnection(
                    success=True,
                    app_key=app_key,
                    mcp_url=connection_info.mcp_url,
                    auth_url=connection_info.mcp_url,
                    qualified_name=f"composio/{app_key}"
                )
            else:
                return ComposioMCPConnection(
                    success=False,
                    app_key=app_key,
                    error=connection_result.error or "OAuth completion failed"
                )
                
        except Exception as e:
            logger.error(f"Error waiting for OAuth completion: {e}")
            return ComposioMCPConnection(
                success=False,
                app_key=app_key,
                error=str(e)
            )

    async def get_active_connection(self, user_id: str, app_key: str) -> Optional[ComposioMCPConnection]:
        """
        Get active MCP connection for user and app.
        
        Args:
            user_id: User identifier
            app_key: App key
            
        Returns:
            ComposioMCPConnection if active connection exists, None otherwise
        """
        try:
            connection_info = await composio_oauth_manager.get_connection(user_id, app_key)
            
            if connection_info and connection_info.status == "active":
                return ComposioMCPConnection(
                    success=True,
                    app_key=app_key,
                    mcp_url=connection_info.mcp_url,
                    auth_url=connection_info.mcp_url,
                    qualified_name=f"composio/{app_key}"
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting active connection: {e}")
            return None

    async def list_user_mcp_connections(self, user_id: str) -> List[Dict[str, Any]]:
        """
        List all MCP connections for a user.
        
        This method maintains compatibility with the old interface while using
        the new connection storage.
        
        Args:
            user_id: User identifier
            
        Returns:
            List of connection dictionaries in old format for compatibility
        """
        try:
            connections = await composio_oauth_manager.list_user_connections(user_id)
            
            # Convert to old format for compatibility
            result = []
            for conn in connections:
                app_config = get_composio_app_config(conn.app_key)
                
                result.append({
                    "id": f"composio_{conn.user_id}_{conn.app_key}",
                    "user_id": conn.user_id,
                    "qualified_name": f"composio/{conn.app_key}",
                    "app_key": conn.app_key,
                    "app_name": app_config.get("name", conn.app_key.title()),
                    "mcp_url": conn.mcp_url,
                    "auth_url": conn.redirect_url if conn.status == "pending" else conn.mcp_url,
                    "status": conn.status,
                    "created_at": conn.metadata.get("initiated_at") if conn.metadata else None,
                    "updated_at": None,
                    "expires_at": conn.expires_at.isoformat() if conn.expires_at else None,
                    "scope": f"composio_{conn.app_key}",
                    "connection_id": conn.connection_id,
                    "connected_account_id": conn.connected_account_id
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error listing user MCP connections: {e}")
            return []

    async def delete_user_mcp_connection(self, user_id: str, app_key: str) -> bool:
        """
        Delete MCP connection for user and app.
        
        Args:
            user_id: User identifier
            app_key: App key
            
        Returns:
            True if successful, False otherwise
        """
        try:
            return await composio_oauth_manager.delete_connection(user_id, app_key)
        except Exception as e:
            logger.error(f"Error deleting MCP connection: {e}")
            return False

    async def update_mcp_enabled_tools(
        self, user_id: str, app_key: str, selected_tools: List[str]
    ) -> bool:
        """
        Update enabled tools for MCP connection and store in agents table.
        
        This method maintains compatibility with the existing tool selection flow
        while using the new connection management.
        
        Args:
            user_id: User identifier
            app_key: App key
            selected_tools: List of selected tool names
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Updating enabled tools for {app_key}: {selected_tools}")
            
            # Get active connection
            connection_info = await composio_oauth_manager.get_connection(user_id, app_key)
            if not connection_info or connection_info.status != "active":
                logger.error(f"No active connection found for {app_key}")
                return False
            
            # Import here to avoid circular imports
            from services.supabase import DBConnection
            
            db = DBConnection()
            supabase = await db.client
            
            # Get user's account ID
            account_result = (
                supabase.schema("basejump")
                .table("accounts")
                .select("id")
                .eq("primary_owner_user_id", user_id)
                .eq("personal_account", True)
                .execute()
            )
            
            if not account_result.data:
                logger.error(f"No personal account found for user {user_id}")
                return False
            
            account_id = account_result.data[0]["id"]
            
            # Get or create default agent
            default_agent_result = (
                supabase.table("agents")
                .select("agent_id, custom_mcps")
                .eq("account_id", account_id)
                .eq("is_default", True)
                .execute()
            )
            
            if not default_agent_result.data:
                # Create default agent if none exists
                from utils.default_agent_config import get_default_agent_config
                
                create_result = (
                    supabase.table("agents")
                    .insert(get_default_agent_config(account_id))
                    .execute()
                )
                
                if not create_result.data:
                    logger.error(f"Failed to create default agent for account {account_id}")
                    return False
                
                default_agent_id = create_result.data[0]["agent_id"]
                current_custom_mcps = []
            else:
                default_agent_id = default_agent_result.data[0]["agent_id"]
                current_custom_mcps = default_agent_result.data[0]["custom_mcps"] or []
            
            # Find existing MCP or create new one
            updated = False
            for i, mcp in enumerate(current_custom_mcps):
                if (
                    mcp.get("type") == "http"
                    and mcp.get("name", "").lower() == app_key.lower()
                ):
                    # Update existing MCP
                    current_custom_mcps[i]["enabledTools"] = selected_tools
                    current_custom_mcps[i]["config"]["url"] = connection_info.mcp_url
                    updated = True
                    break
            
            if not updated:
                # Add new MCP
                new_mcp = {
                    "type": "http",
                    "name": app_key.title(),
                    "config": {"url": connection_info.mcp_url},
                    "enabledTools": selected_tools,
                }
                current_custom_mcps.append(new_mcp)
            
            # Update agent
            update_result = (
                supabase.table("agents")
                .update({"custom_mcps": current_custom_mcps})
                .eq("agent_id", default_agent_id)
                .execute()
            )
            
            if update_result.data:
                logger.info(f"Successfully updated enabled tools for {app_key}")
                return True
            else:
                logger.error(f"Failed to update agent with new tool selection")
                return False
                
        except Exception as e:
            logger.error(f"Error updating MCP enabled tools: {e}")
            return False

    def get_supported_apps(self) -> List[str]:
        """
        Get list of supported apps.
        
        Returns:
            List of supported app keys
        """
        return get_supported_composio_apps()

    async def refresh_mcp_connection_after_auth(self, user_id: str, app_key: str) -> bool:
        """
        Refresh MCP connection after OAuth completion.
        
        This method is called after OAuth completion to update the MCP URL
        in the agents table.
        
        Args:
            user_id: User identifier
            app_key: App key
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Refreshing MCP connection after auth for {app_key}")
            
            # Get active connection
            connection_info = await composio_oauth_manager.get_connection(user_id, app_key)
            if not connection_info or connection_info.status != "active":
                logger.error(f"No active connection found for {app_key}")
                return False
            
            # Update the MCP URL in agents table
            # This is similar to update_mcp_enabled_tools but only updates the URL
            return await self._update_agent_mcp_url(user_id, app_key, connection_info.mcp_url)
            
        except Exception as e:
            logger.error(f"Error refreshing MCP connection: {e}")
            return False

    async def _update_agent_mcp_url(self, user_id: str, app_key: str, mcp_url: str) -> bool:
        """Update MCP URL in agent's custom_mcps."""
        try:
            # Import here to avoid circular imports
            from services.supabase import DBConnection
            
            db = DBConnection()
            supabase = await db.client
            
            # Get user's account ID
            account_result = (
                supabase.schema("basejump")
                .table("accounts")
                .select("id")
                .eq("primary_owner_user_id", user_id)
                .eq("personal_account", True)
                .execute()
            )
            
            if not account_result.data:
                return False
            
            account_id = account_result.data[0]["id"]
            
            # Get default agent
            default_agent_result = (
                supabase.table("agents")
                .select("agent_id, custom_mcps")
                .eq("account_id", account_id)
                .eq("is_default", True)
                .execute()
            )
            
            if not default_agent_result.data:
                return False
            
            default_agent_id = default_agent_result.data[0]["agent_id"]
            current_custom_mcps = default_agent_result.data[0]["custom_mcps"] or []
            
            # Update MCP URL
            for i, mcp in enumerate(current_custom_mcps):
                if (
                    mcp.get("type") == "http"
                    and mcp.get("name", "").lower() == app_key.lower()
                ):
                    current_custom_mcps[i]["config"]["url"] = mcp_url
                    break
            
            # Update agent
            update_result = (
                supabase.table("agents")
                .update({"custom_mcps": current_custom_mcps})
                .eq("agent_id", default_agent_id)
                .execute()
            )
            
            return bool(update_result.data)
            
        except Exception as e:
            logger.error(f"Error updating agent MCP URL: {e}")
            return False


# Create singleton instance
composio_mcp_service_new = ComposioMCPServiceNew()
