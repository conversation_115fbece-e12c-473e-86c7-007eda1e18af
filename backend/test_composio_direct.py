"""
Direct Composio SDK Test

This script tests the Composio SDK directly to validate:
1. Integration IDs are correct
2. Entity creation works
3. OAuth connection initiation works
4. Redirect URLs are generated

Usage:
    python test_composio_direct.py
"""

import asyncio
import sys
import os
import hashlib
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.logger import logger
from constants.composio_mcp_constants import get_composio_integration_id

# Import Composio SDK directly
try:
    from composio import ComposioToolSet
    COMPOSIO_AVAILABLE = True
    logger.info("✅ Composio SDK imported successfully")
except ImportError:
    logger.error("❌ composio-core not installed. Install with: pip install composio-core")
    ComposioToolSet = None
    COMPOSIO_AVAILABLE = False


class DirectComposioTest:
    """Test Composio SDK directly without service layer dependencies."""

    def __init__(self):
        self.test_user_id = "test-user-550e8400"  # Simple test user ID
        self.test_apps = ["notion", "gmail"]  # Apps with real integration IDs
        
        # Initialize Composio SDK
        self.toolset = None
        if COMPOSIO_AVAILABLE:
            try:
                self.toolset = ComposioToolSet()
                logger.info("✅ Composio SDK initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Composio SDK: {e}")
                self.toolset = None
        else:
            logger.error("❌ Composio SDK not available")

    def _generate_entity_id(self, user_id: str) -> str:
        """Generate deterministic entity ID for user."""
        # Create a deterministic entity ID based on user ID
        hash_input = f"atlas_{user_id}".encode('utf-8')
        hash_digest = hashlib.sha256(hash_input).hexdigest()[:16]
        return f"atlas_{hash_digest}"

    def test_integration_ids(self) -> Dict[str, Any]:
        """Test that integration IDs are available for our apps."""
        result = {
            "success": True,
            "apps_tested": [],
            "missing_integration_ids": [],
            "found_integration_ids": {}
        }
        
        logger.info("🔍 Testing integration IDs...")
        
        for app_key in self.test_apps:
            result["apps_tested"].append(app_key)
            integration_id = get_composio_integration_id(app_key)
            
            if integration_id:
                result["found_integration_ids"][app_key] = integration_id
                logger.info(f"✅ {app_key}: {integration_id}")
            else:
                result["missing_integration_ids"].append(app_key)
                result["success"] = False
                logger.error(f"❌ {app_key}: No integration ID found")
        
        return result

    def test_sdk_initialization(self) -> Dict[str, Any]:
        """Test Composio SDK initialization."""
        result = {
            "sdk_available": COMPOSIO_AVAILABLE,
            "toolset_initialized": self.toolset is not None,
            "success": False,
            "error": None
        }
        
        logger.info("🔧 Testing SDK initialization...")
        
        if not COMPOSIO_AVAILABLE:
            result["error"] = "Composio SDK not available"
            logger.error("❌ Composio SDK not available")
            return result
            
        if not self.toolset:
            result["error"] = "Toolset not initialized"
            logger.error("❌ Toolset not initialized")
            return result
        
        result["success"] = True
        logger.info("✅ SDK initialization successful")
        return result

    def test_entity_creation(self) -> Dict[str, Any]:
        """Test entity creation with Composio SDK."""
        result = {
            "success": False,
            "entity_id": None,
            "error": None
        }
        
        logger.info("👤 Testing entity creation...")
        
        if not self.toolset:
            result["error"] = "Toolset not available"
            return result
        
        try:
            entity_id = self._generate_entity_id(self.test_user_id)
            result["entity_id"] = entity_id
            
            logger.info(f"📝 Creating entity: {entity_id}")
            entity = self.toolset.get_entity(id=entity_id)
            
            result["success"] = True
            logger.info(f"✅ Entity created/retrieved successfully: {entity_id}")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ Entity creation failed: {e}")
        
        return result

    def test_oauth_initiation(self, app_key: str) -> Dict[str, Any]:
        """Test OAuth connection initiation for a specific app."""
        result = {
            "app_key": app_key,
            "success": False,
            "integration_id": None,
            "entity_id": None,
            "redirect_url": None,
            "error": None
        }
        
        logger.info(f"🔗 Testing OAuth initiation for {app_key}...")
        
        if not self.toolset:
            result["error"] = "Toolset not available"
            return result
        
        try:
            # Get integration ID
            integration_id = get_composio_integration_id(app_key)
            if not integration_id:
                result["error"] = f"No integration ID found for {app_key}"
                return result
            
            result["integration_id"] = integration_id
            logger.info(f"📋 Using integration ID: {integration_id}")
            
            # Create entity
            entity_id = self._generate_entity_id(self.test_user_id)
            result["entity_id"] = entity_id
            
            entity = self.toolset.get_entity(id=entity_id)
            logger.info(f"👤 Using entity: {entity_id}")
            
            # Initiate connection
            logger.info(f"🚀 Initiating OAuth connection...")
            connection_request = entity.initiate_connection(integration_id)
            
            # Extract redirect URL
            redirect_url = getattr(connection_request, 'redirectUrl', None)
            if redirect_url:
                result["redirect_url"] = redirect_url
                result["success"] = True
                logger.info(f"✅ OAuth initiated successfully")
                logger.info(f"🔗 Redirect URL: {redirect_url}")
            else:
                result["error"] = "No redirect URL returned from Composio"
                logger.error("❌ No redirect URL returned")
                
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ OAuth initiation failed: {e}")
        
        return result

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return comprehensive results."""
        logger.info("🧪 Starting Direct Composio SDK Tests")
        logger.info("=" * 60)
        
        results = {
            "integration_ids": self.test_integration_ids(),
            "sdk_initialization": self.test_sdk_initialization(),
            "entity_creation": self.test_entity_creation(),
            "oauth_tests": {}
        }
        
        # Only test OAuth if previous tests passed
        if results["sdk_initialization"]["success"] and results["entity_creation"]["success"]:
            for app_key in self.test_apps:
                if app_key in results["integration_ids"]["found_integration_ids"]:
                    logger.info(f"\n🔄 Testing OAuth for {app_key.upper()}")
                    results["oauth_tests"][app_key] = self.test_oauth_initiation(app_key)
        
        return results

    def print_summary(self, results: Dict[str, Any]):
        """Print a summary of all test results."""
        logger.info("\n" + "=" * 60)
        logger.info("🎯 DIRECT COMPOSIO SDK TEST SUMMARY")
        logger.info("=" * 60)
        
        # Integration IDs
        integration_results = results["integration_ids"]
        logger.info(f"\n📋 Integration IDs: {'✅ PASS' if integration_results['success'] else '❌ FAIL'}")
        for app, id in integration_results["found_integration_ids"].items():
            logger.info(f"   {app}: {id}")
        
        # SDK Initialization
        sdk_results = results["sdk_initialization"]
        logger.info(f"\n🔧 SDK Initialization: {'✅ PASS' if sdk_results['success'] else '❌ FAIL'}")
        if sdk_results["error"]:
            logger.info(f"   Error: {sdk_results['error']}")
        
        # Entity Creation
        entity_results = results["entity_creation"]
        logger.info(f"\n👤 Entity Creation: {'✅ PASS' if entity_results['success'] else '❌ FAIL'}")
        if entity_results["error"]:
            logger.info(f"   Error: {entity_results['error']}")
        
        # OAuth Tests
        oauth_results = results["oauth_tests"]
        logger.info(f"\n🔗 OAuth Tests:")
        for app, result in oauth_results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            logger.info(f"   {app}: {status}")
            if result["redirect_url"]:
                logger.info(f"      Redirect URL: {result['redirect_url']}")
            if result["error"]:
                logger.info(f"      Error: {result['error']}")
        
        logger.info("\n" + "=" * 60)


async def main():
    """Main test function."""
    test_runner = DirectComposioTest()
    
    try:
        results = test_runner.run_all_tests()
        test_runner.print_summary(results)
        
        # Check overall success
        success_count = sum([
            1 if results["integration_ids"]["success"] else 0,
            1 if results["sdk_initialization"]["success"] else 0,
            1 if results["entity_creation"]["success"] else 0,
            sum(1 for r in results["oauth_tests"].values() if r["success"])
        ])
        
        total_tests = 3 + len(results["oauth_tests"])
        
        if success_count == total_tests:
            logger.info(f"\n🎉 All {total_tests} tests passed!")
            return 0
        else:
            logger.warning(f"\n⚠️  {success_count}/{total_tests} tests passed.")
            return 1
            
    except Exception as e:
        logger.error(f"Test runner failed with exception: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
