"""
Comprehensive Test for Complete Composio OAuth Flow

This script tests the complete OAuth flow from initiation through completion,
displaying both the redirect URL for manual testing and the final MCP URL
that gets stored in the database.

Test Flow:
1. Initiate OAuth connection
2. Display redirect URL for manual authentication
3. Wait for OAuth completion (with timeout)
4. Extract connected_account_id
5. Generate and display final MCP URL
6. Verify storage in database
"""

import asyncio
import sys
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.logger import logger
from services.composio_oauth_manager import composio_oauth_manager
from constants.composio_mcp_constants import get_supported_composio_apps


async def test_complete_oauth_flow():
    """Test the complete OAuth flow with real Composio SDK integration."""

    logger.info("🧪 Testing Complete Composio OAuth Flow")
    logger.info("=" * 60)

    # Test configuration - use a valid UUID format
    test_user_id = "550e8400-e29b-41d4-a716-************"  # Valid UUID for testing
    test_apps = ["gmail", "notion"]  # Apps with real integration IDs

    results = {}

    for app_key in test_apps:
        logger.info(f"\n🔄 Testing Complete OAuth Flow for {app_key.upper()}")
        logger.info("-" * 50)

        try:
            # Step 1: Initiate OAuth connection
            logger.info(f"📝 Step 1: Initiating OAuth connection for {app_key}...")
            connection_result = await composio_oauth_manager.initiate_connection(
                test_user_id, app_key
            )

            if not connection_result.success:
                results[app_key] = {
                    "step": "initiation",
                    "success": False,
                    "error": connection_result.error,
                    "redirect_url": None,
                    "mcp_url": None,
                }
                logger.error(
                    f"❌ OAuth initiation failed for {app_key}: {connection_result.error}"
                )
                continue

            connection_info = connection_result.connection_info
            redirect_url = connection_info.redirect_url

            logger.info(f"✅ OAuth initiated successfully for {app_key}")
            logger.info(f"🔗 Redirect URL: {redirect_url}")
            logger.info(f"📊 Connection Status: {connection_info.status}")
            logger.info(f"🆔 Entity ID: {connection_info.entity_id}")

            # Step 2: Display redirect URL for manual testing
            logger.info(f"\n📱 Step 2: Manual Authentication Required")
            logger.info(f"🌐 Please open this URL in your browser to complete OAuth:")
            logger.info(f"🔗 {redirect_url}")
            logger.info(f"⏰ You have 3 minutes to complete authentication...")

            # Step 3: Wait for OAuth completion
            logger.info(f"\n⏳ Step 3: Waiting for OAuth completion...")
            activation_result = (
                await composio_oauth_manager.wait_for_connection_activation(
                    test_user_id, app_key, timeout=180  # 3 minutes
                )
            )

            if not activation_result.success:
                results[app_key] = {
                    "step": "activation",
                    "success": False,
                    "error": activation_result.error,
                    "redirect_url": redirect_url,
                    "mcp_url": None,
                }
                logger.error(
                    f"❌ OAuth activation failed for {app_key}: {activation_result.error}"
                )
                continue

            # Step 4: Extract results
            final_connection_info = activation_result.connection_info
            connected_account_id = final_connection_info.connected_account_id
            mcp_url = final_connection_info.mcp_url

            logger.info(f"✅ OAuth completed successfully for {app_key}")
            logger.info(f"🔑 Connected Account ID: {connected_account_id}")
            logger.info(f"🌐 Final MCP URL: {mcp_url}")
            logger.info(f"📊 Final Status: {final_connection_info.status}")

            # Step 5: Verify storage
            logger.info(f"\n🗄️ Step 4: Verifying database storage...")
            stored_connection = await composio_oauth_manager.get_connection(
                test_user_id, app_key
            )

            if stored_connection and stored_connection.status == "active":
                logger.info(f"✅ Connection properly stored in database")
                logger.info(f"📊 Stored Status: {stored_connection.status}")
                logger.info(
                    f"🔑 Stored Connected Account ID: {stored_connection.connected_account_id}"
                )
                logger.info(f"🌐 Stored MCP URL: {stored_connection.mcp_url}")
            else:
                logger.warning(f"⚠️ Connection not found in database or not active")

            results[app_key] = {
                "step": "complete",
                "success": True,
                "error": None,
                "redirect_url": redirect_url,
                "connected_account_id": connected_account_id,
                "mcp_url": mcp_url,
                "stored_properly": stored_connection
                and stored_connection.status == "active",
            }

        except Exception as e:
            results[app_key] = {
                "step": "exception",
                "success": False,
                "error": str(e),
                "redirect_url": None,
                "mcp_url": None,
            }
            logger.error(f"❌ Exception during {app_key} test: {e}")

    # Print comprehensive summary
    logger.info("\n" + "=" * 60)
    logger.info("🎯 COMPLETE OAUTH FLOW TEST SUMMARY")
    logger.info("=" * 60)

    success_count = 0
    for app_key, result in results.items():
        logger.info(f"\n📱 {app_key.upper()} Results:")

        if result["success"]:
            logger.info(f"   ✅ Status: SUCCESS")
            logger.info(f"   🔗 Redirect URL: {result['redirect_url']}")
            logger.info(f"   🔑 Connected Account ID: {result['connected_account_id']}")
            logger.info(f"   🌐 MCP URL: {result['mcp_url']}")
            logger.info(
                f"   🗄️ Stored in DB: {'✅' if result.get('stored_properly') else '❌'}"
            )
            success_count += 1
        else:
            logger.info(f"   ❌ Status: FAILED at {result['step']}")
            logger.info(f"   🔗 Redirect URL: {result['redirect_url'] or 'N/A'}")
            logger.info(f"   ❌ Error: {result['error']}")

    logger.info(
        f"\n🎉 {success_count}/{len(test_apps)} apps completed OAuth flow successfully!"
    )

    if success_count > 0:
        logger.info("\n💡 OAuth Flow Status:")
        logger.info("✅ OAuth initiation working correctly")
        logger.info("✅ Redirect URL generation functional")
        logger.info("✅ wait_until_active() implementation working")
        logger.info("✅ Connected account ID extraction working")
        logger.info("✅ MCP URL generation working")
        logger.info("✅ Database storage working")
        logger.info("✅ Complete flow ready for production")
        return True
    else:
        logger.warning("\n⚠️ OAuth flow needs attention")
        return False


async def main():
    """Main test function."""
    try:
        # Display supported apps
        supported_apps = get_supported_composio_apps()
        logger.info(f"📋 Supported apps: {', '.join(supported_apps)}")

        success = await test_complete_oauth_flow()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
