"""
Quick test for real Composio OAuth flow with actual integration IDs.

This script tests the complete flow:
1. Creates entity for user
2. Initiates OAuth connection
3. Returns redirect URL for manual authentication
4. Simulates waiting for OAuth completion
5. Returns final MCP URL

Usage:
    python test_real_composio_flow.py
"""

import asyncio
import sys
import os
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.logger import logger
from services.composio_entity_manager import composio_entity_manager
from services.composio_oauth_manager import composio_oauth_manager
from services.composio_mcp_service_new import composio_mcp_service_new


class RealComposioFlowTest:
    """Test the real Composio OAuth flow with actual integration IDs."""

    def __init__(self):
        # Use a real UUID format for testing
        self.test_user_id = "550e8400-e29b-41d4-a716-************"  # Test user UUID
        self.test_apps = ["notion", "gmail"]  # Apps with real integration IDs

    async def test_complete_flow(self, app_key: str) -> Dict[str, Any]:
        """
        Test the complete OAuth flow for an app.
        
        Args:
            app_key: The app to test (e.g., "notion", "gmail")
            
        Returns:
            Dictionary with test results including redirect URL and MCP URL
        """
        result = {
            "app_key": app_key,
            "success": False,
            "entity_created": False,
            "oauth_initiated": False,
            "redirect_url": None,
            "mcp_url": None,
            "error": None
        }
        
        try:
            logger.info(f"🚀 Testing complete flow for {app_key}")
            
            # Step 1: Create entity for user
            logger.info(f"📝 Step 1: Creating entity for user {self.test_user_id}")
            entity_info = await composio_entity_manager.get_or_create_entity(self.test_user_id)
            
            if entity_info:
                result["entity_created"] = True
                logger.info(f"✅ Entity created: {entity_info.entity_id}")
            else:
                result["error"] = "Failed to create entity"
                return result
            
            # Step 2: Initiate OAuth connection
            logger.info(f"🔗 Step 2: Initiating OAuth connection for {app_key}")
            connection_result = await composio_oauth_manager.initiate_connection(
                self.test_user_id, app_key
            )
            
            if connection_result.success and connection_result.connection_info:
                result["oauth_initiated"] = True
                result["redirect_url"] = connection_result.connection_info.redirect_url
                logger.info(f"✅ OAuth initiated successfully")
                logger.info(f"🔗 Redirect URL: {result['redirect_url']}")
            else:
                result["error"] = connection_result.error
                return result
            
            # Step 3: Simulate OAuth completion (in real scenario, user would authenticate)
            logger.info(f"⏳ Step 3: Simulating OAuth completion...")
            logger.info(f"📋 In a real scenario, user would visit the redirect URL to authenticate")
            
            # For testing, we'll simulate a successful connection
            # In reality, this would happen after user completes OAuth
            connection_info = connection_result.connection_info
            
            # Simulate successful connection
            connection_info.status = "active"
            connection_info.connection_id = f"conn_{self.test_user_id}_{app_key}"
            connection_info.connected_account_id = f"acc_{self.test_user_id}_{app_key}"
            
            # Generate MCP URL
            from constants.composio_mcp_constants import get_composio_mcp_url_with_connected_account
            mcp_url = get_composio_mcp_url_with_connected_account(
                app_key, connection_info.connected_account_id
            )
            
            if mcp_url:
                result["mcp_url"] = mcp_url
                logger.info(f"✅ MCP URL generated: {mcp_url}")
            
            result["success"] = True
            logger.info(f"🎉 Complete flow test successful for {app_key}")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ Error in complete flow test: {e}")
        
        return result

    async def test_mcp_service_flow(self, app_key: str) -> Dict[str, Any]:
        """
        Test the MCP service flow (what the API endpoints use).
        
        Args:
            app_key: The app to test
            
        Returns:
            Dictionary with test results
        """
        result = {
            "app_key": app_key,
            "success": False,
            "connection_created": False,
            "redirect_url": None,
            "error": None
        }
        
        try:
            logger.info(f"🔧 Testing MCP service flow for {app_key}")
            
            # Test the main MCP service method (used by API endpoints)
            connection = await composio_mcp_service_new.create_user_mcp_connection_no_storage(
                self.test_user_id, app_key
            )
            
            if connection.success:
                result["connection_created"] = True
                result["redirect_url"] = connection.auth_url
                result["success"] = True
                logger.info(f"✅ MCP service flow successful")
                logger.info(f"🔗 Auth URL: {result['redirect_url']}")
            else:
                result["error"] = connection.error
                logger.error(f"❌ MCP service flow failed: {connection.error}")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ Error in MCP service flow test: {e}")
        
        return result

    async def run_all_tests(self):
        """Run tests for all configured apps."""
        logger.info("🧪 Starting Real Composio Flow Tests")
        logger.info("=" * 60)
        
        all_results = []
        
        for app_key in self.test_apps:
            logger.info(f"\n📱 Testing {app_key.upper()}")
            logger.info("-" * 40)
            
            # Test complete flow
            logger.info(f"🔄 Testing complete OAuth flow...")
            complete_result = await self.test_complete_flow(app_key)
            
            # Test MCP service flow
            logger.info(f"🔄 Testing MCP service flow...")
            mcp_result = await self.test_mcp_service_flow(app_key)
            
            # Combine results
            combined_result = {
                "app_key": app_key,
                "complete_flow": complete_result,
                "mcp_service_flow": mcp_result
            }
            
            all_results.append(combined_result)
        
        # Print summary
        self.print_summary(all_results)
        return all_results

    def print_summary(self, results):
        """Print a summary of all test results."""
        logger.info("\n" + "=" * 60)
        logger.info("🎯 REAL COMPOSIO FLOW TEST SUMMARY")
        logger.info("=" * 60)
        
        for result in results:
            app_key = result["app_key"]
            complete = result["complete_flow"]
            mcp = result["mcp_service_flow"]
            
            logger.info(f"\n📱 {app_key.upper()} Results:")
            logger.info(f"   Complete Flow: {'✅ SUCCESS' if complete['success'] else '❌ FAILED'}")
            logger.info(f"   MCP Service:   {'✅ SUCCESS' if mcp['success'] else '❌ FAILED'}")
            
            if complete["redirect_url"]:
                logger.info(f"   🔗 Redirect URL: {complete['redirect_url']}")
            
            if complete["mcp_url"]:
                logger.info(f"   🌐 MCP URL: {complete['mcp_url']}")
            
            if complete["error"]:
                logger.info(f"   ❌ Error: {complete['error']}")
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 Test completed! Use the redirect URLs above to test authentication.")
        logger.info("💡 After authenticating, the MCP URLs can be used in your agents.")


async def main():
    """Main test function."""
    test_runner = RealComposioFlowTest()
    
    try:
        results = await test_runner.run_all_tests()
        
        # Check if any tests were successful
        success_count = sum(1 for r in results if r["complete_flow"]["success"] or r["mcp_service_flow"]["success"])
        
        if success_count > 0:
            logger.info(f"\n🎉 {success_count} out of {len(results)} apps tested successfully!")
            return 0
        else:
            logger.warning(f"\n⚠️  All tests failed. Check the errors above.")
            return 1
            
    except Exception as e:
        logger.error(f"Test runner failed with exception: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
