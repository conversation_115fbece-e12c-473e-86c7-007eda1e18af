# Composio Python SDK Methods and APIs

## `toolset.initiate_connection()` Method

The `initiate_connection()` method is implemented in the `_IntegrationMixin` class and has the following signature and behavior:

**Parameters:**

- `integration_id: Optional[str]` - ID of existing integration to use
- `app: Optional[AppType]` - App to create/use integration for
- `labels: Optional[List]` - Labels for the connected account
- `entity_id: Optional[str]` - Entity ID (defaults to toolset's entity_id)
- `redirect_url: Optional[str]` - Redirect URL for OAuth flows
- `connected_account_params: Optional[Dict]` - Parameters required for connection
- `auth_scheme: Optional[AuthSchemeType]` - Authentication scheme to use
- `auth_config: Optional[Dict[str, Any]]` - Authentication configuration

**Return Object:**
Returns a `ConnectionRequestModel` with properties:

- `connectionStatus: str` - Status of the connection
- `connectedAccountId: str` - ID of the connected account
- `redirectUrl: Optional[str]` - Redirect URL if applicable

**Error Handling:**

- Raises `InvalidParams` if both `integration_id` and `app` are None
- Raises `InvalidParams` if auth_scheme is not in supported schemes
- Raises `InvalidParams` for missing required `connected_account_params`
- Validates auth schemes against `AUTH_SCHEME_WITH_INITIATE` list [1](#1-0) [2](#1-1)

## `connection_request.wait_until_active()` Method

**Parameters:**

- `client: "Composio"` - Composio client instance
- `timeout: float = 60.0` - Timeout in seconds (default 60)

**Return Object:**
Returns a `ConnectedAccountModel` when the connection becomes active.

**Error Handling:**

- Raises `SDKTimeoutError` if connection doesn't become active within timeout period
- Polls connection status every 1 second until status equals "ACTIVE" [3](#1-2)

## Integration Creation API and CLI Commands

### CLI Command

The exact CLI command to create integrations is:

```bash
composio add <app_name> [options]
```

**Options include:**

- `--integration-id/-i` - Use existing integration ID
- `--auth-mode/-a` - Specify authentication mode
- `--scope/-s` - Specify scopes (multiple allowed)
- `--force` - Override existing account
- `--label/-l` - Add labels (multiple allowed) [4](#1-3)

### API Method

The exact API call is `client.integrations.create()` with parameters:

- `app_id: str` - App ID (required)
- `name: Optional[str]` - Integration name
- `auth_mode: Optional[AuthSchemeType]` - Authentication mode
- `auth_config: Optional[Dict[str, Any]]` - Auth configuration
- `use_composio_auth: bool = False` - Use Composio's auth
- `force_new_integration: bool = False` - Force new integration creation [5](#1-4)

### Integration ID Format

Integration IDs are UUIDs returned in the `IntegrationModel` with properties:

- `id: str` - The integration ID (UUID format)
- `name: str` - Integration name
- `authScheme: str` - Authentication scheme used
- `appId: str` - Associated app ID
- `appName: str` - Associated app name [6](#1-5)

## MCP URL Format

### Precise URL Structure

The MCP URL format is:

```
https://mcp.composio.dev/composio/server/<UUID>?transport=sse
```

### Required Parameters

The URL accepts three optional query parameters:

- `user_id` - Bind session to user identifier
- `connected_account_id` - Pin session to specific Composio connected account
- `include_composio_helper_actions=true` - Inject helper tools for authentication

### Server ID Source

The `server_id` (UUID in the URL) is returned when creating an MCP server via:

```bash
curl -X POST https://backend.composio.dev/api/v3/mcp/servers \
  -H "x-api-key: <YOUR_API_KEY>" \
  -H "Content-Type: application/json" \
  -d '{"name": "Gmail", "apps": ["gmail"], "auth_config_id": {}}'
```

The response includes the `id` field which becomes the `server_id` in the MCP URL. [7](#1-6)

## Notes

- The `initiate_connection()` method automatically creates integrations if none exist for the specified app
- Both `toolset.initiate_connection()` and `entity.initiate_connection()` have similar functionality but different implementations
- The `wait_until_active()` method is essential for OAuth flows where user authorization is required
- MCP server URLs use SSE transport by default, but this will be deprecated in favor of native MCP protocol
- Integration creation requires valid app IDs which can be obtained from the apps collection
